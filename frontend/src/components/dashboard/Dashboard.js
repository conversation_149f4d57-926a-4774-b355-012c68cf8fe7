import React from 'react';
import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import useDashboardStore from '../../store/dashboardStore';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { useAuth } from '../../context/AuthContext';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
);

const Dashboard = () => {
  const { currentUser } = useAuth();
  const {
    dashboardData,
    invoicesByMonth,
    topVendors,
    topItems,
    priceAlerts,
    loading,
    error,
    fetchAllDashboardData,
  } = useDashboardStore();

  useEffect(() => {
    console.log('Dashboard: Component mounted, fetching data...');
    fetchAllDashboardData();
  }, [fetchAllDashboardData]);

  // Debug logging for data changes
  useEffect(() => {
    console.log('Dashboard: Data updated', {
      dashboardData,
      invoicesByMonth: invoicesByMonth?.length || 0,
      topVendors: topVendors?.length || 0,
      topItems: topItems?.length || 0,
      priceAlerts: priceAlerts?.length || 0,
      loading,
      error,
    });
  }, [dashboardData, invoicesByMonth, topVendors, topItems, priceAlerts, loading, error]);

  // Chart data for monthly spending
  const monthlySpendingData = {
    labels: invoicesByMonth.map((item) => item.name),
    datasets: [
      {
        label: 'Monthly Spending',
        data: invoicesByMonth.map((item) => item.amount),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        fill: false,
      },
    ],
  };

  // Debug chart data
  console.log('Dashboard: Monthly spending chart data', {
    labels: monthlySpendingData.labels,
    data: monthlySpendingData.datasets[0].data,
    invoicesByMonthLength: invoicesByMonth.length,
  });

  // Chart data for top vendors
  const vendorChartData = {
    labels: topVendors.map((vendor) => vendor.name),
    datasets: [
      {
        label: 'Spending by Vendor',
        data: topVendors.map((vendor) => vendor.amount),
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for top items
  const itemsChartData = {
    labels: topItems.map((item) => item.name),
    datasets: [
      {
        label: 'Top Items by Spending',
        data: topItems.map((item) => item.amount),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Debug chart data
  console.log('Dashboard: Vendor chart data', {
    labels: vendorChartData.labels,
    data: vendorChartData.datasets[0].data,
    topVendorsLength: topVendors.length,
  });

  console.log('Dashboard: Items chart data', {
    labels: itemsChartData.labels,
    data: itemsChartData.datasets[0].data,
    topItemsLength: topItems.length,
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // Show error as a notification instead of replacing the entire component
  const renderError = () => {
    if (!error) return null;

    return (
      <div
        className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6"
        role="alert"
      >
        <span className="block sm:inline">{error}</span>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          {currentUser?.restaurant_name && (
            <p className="text-sm text-gray-500">{currentUser.restaurant_name}</p>
          )}
        </div>
        <Link
          to="/ocr"
          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Scan New Invoice
        </Link>
      </div>

      {/* Display error notification if there's an error */}
      {renderError()}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-primary-100 text-primary-800">
              <svg
                className="h-8 w-8"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Invoices</p>
              <p className="text-lg font-semibold text-gray-900">
                {dashboardData?.totalInvoices || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-800">
              <svg
                className="h-8 w-8"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Spending</p>
              <p className="text-lg font-semibold text-gray-900">
                ${dashboardData?.totalAmount?.toFixed(2) || '0.00'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-800">
              <svg
                className="h-8 w-8"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Invoice</p>
              <p className="text-lg font-semibold text-gray-900">
                ${dashboardData?.averageAmount?.toFixed(2) || '0.00'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-800">
              <svg
                className="h-8 w-8"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Invoices</p>
              <p className="text-lg font-semibold text-gray-900">
                {dashboardData?.pendingInvoices || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Monthly Spending</h2>
            <Link to="/analytics" className="text-sm text-primary-600 hover:text-primary-800">
              View Details →
            </Link>
          </div>
          {invoicesByMonth.length > 0 ? (
            <div className="h-64">
              {(() => {
                try {
                  return (
                    <Line
                      data={monthlySpendingData}
                      options={{
                        maintainAspectRatio: false,
                        responsive: true,
                        plugins: {
                          legend: {
                            display: false,
                          },
                          tooltip: {
                            callbacks: {
                              label: function (context) {
                                return `$${context.raw.toFixed(2)}`;
                              },
                              title: function (context) {
                                return context[0].label;
                              },
                            },
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            padding: 10,
                            cornerRadius: 4,
                          },
                        },
                        scales: {
                          y: {
                            beginAtZero: true,
                            ticks: {
                              callback: function (value) {
                                return '$' + value;
                              },
                            },
                            grid: {
                              color: 'rgba(0, 0, 0, 0.05)',
                            },
                          },
                          x: {
                            grid: {
                              display: false,
                            },
                          },
                        },
                        interaction: {
                          mode: 'index',
                          intersect: false,
                        },
                        elements: {
                          line: {
                            tension: 0.3, // Smoother curve
                          },
                          point: {
                            radius: 3,
                            hoverRadius: 6,
                          },
                        },
                      }}
                    />
                  );
                } catch (error) {
                  console.error('Dashboard: Error rendering Line chart:', error);
                  return (
                    <div className="flex items-center justify-center h-full text-red-500">
                      Error loading chart
                    </div>
                  );
                }
              })()}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <p className="text-gray-500 mb-4">No monthly data available</p>
              <Link
                to="/ocr"
                className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Scan an Invoice to Get Started
              </Link>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Spending by Vendor</h2>
            <Link to="/analytics" className="text-sm text-primary-600 hover:text-primary-800">
              View Details →
            </Link>
          </div>
          {topVendors.length > 0 ? (
            <div className="h-64">
              <Doughnut
                data={vendorChartData}
                options={{
                  maintainAspectRatio: false,
                  responsive: true,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                      },
                    },
                    tooltip: {
                      callbacks: {
                        label: function (context) {
                          const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                          const percentage = Math.round((context.raw / total) * 100);
                          let label = context.label || '';
                          if (label) {
                            label += ': ';
                          }
                          label += `$${context.raw.toFixed(2)} (${percentage}%)`;
                          return label;
                        },
                      },
                      backgroundColor: 'rgba(0, 0, 0, 0.7)',
                      padding: 10,
                      cornerRadius: 4,
                    },
                  },
                  cutout: '65%',
                }}
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <p className="text-gray-500 mb-4">No vendor data available</p>
              <Link
                to="/ocr"
                className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Scan an Invoice to Get Started
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Top Items */}
      <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-gray-900">Top Items by Spending</h2>
          <Link to="/analytics" className="text-sm text-primary-600 hover:text-primary-800">
            View Details →
          </Link>
        </div>
        <div className="p-6">
          {topItems.length > 0 ? (
            <div className="h-64">
              <Bar
                data={itemsChartData}
                options={{
                  maintainAspectRatio: false,
                  responsive: true,
                  plugins: {
                    legend: {
                      display: false,
                    },
                    tooltip: {
                      callbacks: {
                        label: function (context) {
                          return `$${context.raw.toFixed(2)}`;
                        },
                      },
                      backgroundColor: 'rgba(0, 0, 0, 0.7)',
                      padding: 10,
                      cornerRadius: 4,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        callback: function (value) {
                          return '$' + value;
                        },
                      },
                      grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                      },
                    },
                    x: {
                      grid: {
                        display: false,
                      },
                    },
                  },
                  barThickness: 'flex',
                  maxBarThickness: 35,
                  borderRadius: 4,
                }}
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <p className="text-gray-500 mb-4">No item data available</p>
              <Link
                to="/ocr"
                className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Scan an Invoice to Get Started
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Price Alerts */}
      {priceAlerts && priceAlerts.length > 0 && (
        <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-900">Recent Price Alerts</h2>
            <Link to="/analytics" className="text-sm text-primary-600 hover:text-primary-800">
              View All →
            </Link>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {priceAlerts.slice(0, 5).map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    alert.severity === 'high'
                      ? 'border-red-500 bg-red-50'
                      : alert.severity === 'medium'
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-blue-500 bg-blue-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4
                          className={`text-sm font-medium ${
                            alert.severity === 'high'
                              ? 'text-red-800'
                              : alert.severity === 'medium'
                              ? 'text-yellow-800'
                              : 'text-blue-800'
                          }`}
                        >
                          {alert.title}
                        </h4>
                        <span
                          className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            alert.severity === 'high'
                              ? 'bg-red-100 text-red-800'
                              : alert.severity === 'medium'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {Math.abs(alert.percentage_change).toFixed(1)}%
                        </span>
                      </div>
                      <p
                        className={`mt-1 text-sm ${
                          alert.severity === 'high'
                            ? 'text-red-700'
                            : alert.severity === 'medium'
                            ? 'text-yellow-700'
                            : 'text-blue-700'
                        }`}
                      >
                        {alert.message}
                      </p>
                      <div className="mt-2 text-xs text-gray-500">
                        <span>
                          ${alert.old_price.toFixed(2)} → ${alert.current_price.toFixed(2)}
                        </span>
                        {alert.invoice_number && (
                          <span className="ml-2">• Invoice: {alert.invoice_number}</span>
                        )}
                        <span className="ml-2">
                          • {new Date(alert.date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {priceAlerts.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">No recent price alerts</p>
                <p className="text-sm text-gray-400 mt-1">
                  Price alerts will appear here when item prices change significantly
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/ocr"
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Scan Invoice
          </Link>
          <Link
            to="/invoices/add"
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Add Invoice Manually
          </Link>
          <Link
            to="/invoices"
            className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            View All Invoices
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
