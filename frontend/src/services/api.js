import axios from 'axios';

// Make sure the API URL includes the versioned /api/v1 prefix
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api/v1';

console.log('API Service: Initializing with base URL:', API_BASE_URL);

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Enable credentials for cross-origin requests
  withCredentials: true,
  // Increase timeout for mobile networks
  timeout: 10000,
});

// Add request interceptor
// Add Authorization header from token if available (fallback if cookies don't work)
api.interceptors.request.use(
  (config) => {
    // First try to use cookies
    const token = localStorage.getItem('token');
    if (token) {
      console.log('API: Adding Authorization header from localStorage token');
      config.headers['Authorization'] = `Bearer ${token}`;
    } else {
      console.log('API: No token found in localStorage, relying on HTTP-only cookie');
    }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-TOKEN'] = csrfToken;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Add response interceptor to handle token expiration and enhance error messages
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data ? 'Data present' : 'No data',
      headers: response.headers,
    });
    return response;
  },
  (error) => {
    // Enhanced error handling with more detailed messages
    let enhancedError = error;

    // Log detailed error information for debugging mobile issues
    console.error('API Error Details:', {
      message: error.message,
      name: error.name,
      url: error.config?.url,
      method: error.config?.method,
      baseURL: error.config?.baseURL,
      withCredentials: error.config?.withCredentials,
      timeout: error.config?.timeout,
      responseType: error.config?.responseType,
      xsrfCookieName: error.config?.xsrfCookieName,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      requestHeaders: error.config?.headers,
      responseHeaders: error.response?.headers,
      requestData: error.config?.data,
    });

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error Response:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });

      // Handle specific status codes
      switch (error.response.status) {
        case 401:
          // Token expired or invalid
          window.location.href = '/login?expired=true';
          enhancedError.message = 'Your session has expired. Please log in again.';
          break;
        case 403:
          enhancedError.message = 'You do not have permission to perform this action.';
          break;
        case 404:
          enhancedError.message = 'The requested resource was not found.';
          break;
        case 422:
          enhancedError.message = 'Validation error. Please check your input.';
          break;
        case 500:
          enhancedError.message = 'Server error. Please try again later.';
          break;
        default:
          // Use server-provided message if available
          if (error.response.data && error.response.data.message) {
            enhancedError.message = error.response.data.message;
          }
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API Error Request:', {
        responseURL: error.request.responseURL,
        status: error.request.status,
        responseType: error.request.responseType,
        withCredentials: error.request.withCredentials,
        readyState: error.request.readyState,
        statusText: error.request.statusText,
      });
      enhancedError.message =
        'No response from server. Please check your internet connection and try again.';
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('API Error Setup:', error.message);
      enhancedError.message = 'Error setting up the request. Please try again.';
    }

    return Promise.reject(enhancedError);
  },
);

// Auth services
export const authService = {
  register: (userData) => {
    console.log('API Service: Registering user with data:', {
      ...userData,
      password: '[REDACTED]',
    });
    return api
      .post('/register', userData)
      .then((response) => {
        console.log(
          'API Service: Registration successful, response:',
          response ? { status: response.status, hasData: !!response.data } : 'No response',
        );
        return response;
      })
      .catch((error) => {
        console.error('API Service: Registration error:', {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                data: error.response.data,
              }
            : 'No response data',
        });
        throw error;
      });
  },
  login: (credentials) => {
    console.log('API Service: Logging in user with email:', credentials.email);
    console.log('API Service: Using base URL:', API_BASE_URL);

    // Log device information for debugging
    const deviceInfo = {
      userAgent: navigator.userAgent,
      isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      platform: navigator.platform,
      vendor: navigator.vendor,
    };
    console.log('API Service: Device info:', deviceInfo);

    return api
      .post('/login', credentials, {
        // Ensure these settings for login requests
        timeout: 15000, // Longer timeout for mobile networks
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      })
      .then((response) => {
        console.log(
          'API Service: Login successful, response:',
          response
            ? {
                status: response.status,
                statusText: response.statusText,
                hasData: !!response.data,
                hasAuthCookie: document.cookie.includes('access_token') ? 'yes' : 'no',
                headers: response.headers,
              }
            : 'No response',
        );
        return response;
      })
      .catch((error) => {
        console.error('API Service: Login error:', {
          message: error.message,
          name: error.name,
          stack: error.stack,
          response: error.response
            ? {
                status: error.response.status,
                statusText: error.response.statusText,
                data: error.response.data,
                headers: error.response.headers,
              }
            : 'No response data',
          request: error.request ? 'Request made but no response received' : 'No request made',
          config: error.config
            ? {
                url: error.config.url,
                method: error.config.method,
                baseURL: error.config.baseURL,
                timeout: error.config.timeout,
                headers: error.config.headers,
              }
            : 'No config available',
        });
        throw error;
      });
  },
  getCurrentUser: () => {
    console.log('API Service: Fetching current user');
    return api
      .get('/me')
      .then((response) => {
        console.log('API Service: Current user fetch successful');
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error fetching current user:', error);
        throw error;
      });
  },
  updateProfile: (userData) => {
    console.log('API Service: Updating user profile');
    return api
      .put('/me', userData)
      .then((response) => {
        console.log('API Service: Profile update successful');
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error updating profile:', error);
        throw error;
      });
  },
};

// Multi-page OCR services
export const multiPageOcrService = {
  uploadMultiPageInvoice: (files, vendorName, pageOrder) => {
    console.log('API Service: Uploading multi-page invoice', {
      fileCount: files.length,
      vendor: vendorName,
      pageOrder: pageOrder,
    });

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
      console.log('API Service: Added file:', {
        name: file.name,
        type: file.type,
        size: `${(file.size / 1024).toFixed(2)} KB`,
      });
    });

    if (vendorName) {
      formData.append('vendor_name', vendorName);
    }

    if (pageOrder) {
      formData.append('page_order', JSON.stringify(pageOrder));
    }

    return api
      .post('/ocr/upload-multipage', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 minutes for multi-page processing
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`API Service: Multi-page upload progress: ${percentCompleted}%`);
        },
      })
      .then((response) => {
        console.log('API Service: Multi-page upload successful', {
          status: response.status,
          invoiceId: response.data?.invoice_id,
          pageCount: response.data?.page_count,
          mergedFilename: response.data?.merged_filename,
        });
        return response;
      })
      .catch((error) => {
        console.error('API Service: Multi-page upload error:', {
          message: error.message,
          response: error.response?.data,
        });
        throw error;
      });
  },

  getMultiPageInvoice: (invoiceId) => {
    console.log('API Service: Getting multi-page invoice:', invoiceId);
    return api
      .get(`/ocr/multipage/${invoiceId}`)
      .then((response) => {
        console.log('API Service: Multi-page invoice fetched successfully');
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error fetching multi-page invoice:', error);
        throw error;
      });
  },

  reorderPages: (invoiceId, newPageOrder) => {
    console.log('API Service: Reordering pages for invoice:', invoiceId, newPageOrder);
    return api
      .post(`/ocr/multipage/${invoiceId}/reorder`, {
        page_order: newPageOrder,
      })
      .then((response) => {
        console.log('API Service: Page reordering successful');
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error reordering pages:', error);
        throw error;
      });
  },

  processMultiPageOCR: (invoiceId, vendorName) => {
    console.log('API Service: Processing OCR for multi-page invoice:', invoiceId, vendorName);
    return api
      .post(
        `/ocr/multipage/${invoiceId}/process-ocr`,
        {
          vendor_name: vendorName,
        },
        {
          timeout: 180000, // 3 minutes for OCR processing
        },
      )
      .then((response) => {
        console.log('API Service: Multi-page OCR processing successful', {
          status: response.status,
          lineItemsCount: response.data?.line_items_count,
          confidence: response.data?.ocr_confidence,
        });
        return response;
      })
      .catch((error) => {
        console.error('API Service: Multi-page OCR processing error:', error);
        throw error;
      });
  },
};

// OCR services
export const ocrService = {
  uploadImage: (formData) => {
    console.log('API Service: Uploading image for OCR processing');

    // Log file information for debugging
    if (formData.get('file')) {
      const file = formData.get('file');
      console.log('API Service: File details:', {
        name: file.name,
        type: file.type,
        size: `${(file.size / 1024).toFixed(2)} KB`,
      });
    }

    // Log vendor information if present
    if (formData.get('vendor')) {
      console.log('API Service: Vendor selected:', formData.get('vendor'));
    }

    return api
      .post('/ocr/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Increase timeout for OCR processing (which can take time)
        timeout: 60000, // 60 seconds
        // Show upload progress in console
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`API Service: Upload progress: ${percentCompleted}%`);
        },
      })
      .then((response) => {
        console.log('API Service: OCR processing successful', {
          status: response.status,
          hasData: !!response.data,
          ocrSource: response.data?.ocr_source || 'Unknown',
          confidence: response.data?.confidence || 0,
          lineItems: response.data?.invoice?.line_items?.length || 0,
        });
        return response;
      })
      .catch((error) => {
        console.error('API Service: OCR processing error:', {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                data: error.response.data,
                message: error.response.data?.message || 'No message',
              }
            : 'No response',
          request: error.request ? 'Request made but no response received' : 'No request made',
        });
        throw error;
      });
  },

  getLatestResult: () => {
    console.log('API Service: Getting latest OCR result');
    return api
      .get('/ocr/latest')
      .then((response) => {
        console.log('API Service: Latest OCR result fetched successfully');
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error fetching latest OCR result:', error);
        throw error;
      });
  },
};

// Invoice services
export const invoiceService = {
  getAll: () => api.get('/invoices'),
  getById: (id) => api.get(`/invoices/${id}`),
  create: (invoice) => api.post('/invoices', invoice),
  update: (id, invoice) => api.put(`/invoices/${id}`, invoice),
  delete: (id) => api.delete(`/invoices/${id}`),
  processInvoice: (formData) =>
    api.post('/ocr/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 60 seconds
    }),
  exportToCsv: (invoices) => {
    // Client-side CSV generation
    const headers = ['Invoice Number', 'Vendor', 'Date', 'Amount', 'Status'];
    const csvRows = [
      headers.join(','),
      ...invoices.map((invoice) =>
        [
          invoice.invoice_number || '',
          invoice.vendor_name || '',
          invoice.invoice_date || '',
          invoice.total_amount || 0,
          invoice.status || '',
        ].join(','),
      ),
    ];

    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `invoices-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  exportToPdf: (invoice) => {
    // Import jsPDF and jsPDF-AutoTable
    import('jspdf').then(({ default: jsPDF }) => {
      import('jspdf-autotable').then(() => {
        try {
          // Create a new PDF document
          const doc = new jsPDF();

          // Add title
          doc.setFontSize(20);
          doc.text('Invoice', 14, 22);

          // Add invoice details
          doc.setFontSize(12);
          doc.text(`Invoice Number: ${invoice.invoice_number || 'N/A'}`, 14, 35);
          doc.text(`Vendor: ${invoice.vendor_name || 'N/A'}`, 14, 42);

          // Format date
          let invoiceDate = 'N/A';
          if (invoice.invoice_date) {
            const date = new Date(invoice.invoice_date);
            invoiceDate = new Intl.DateTimeFormat('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            }).format(date);
          }
          doc.text(`Date: ${invoiceDate}`, 14, 49);

          // Format due date if available
          if (invoice.due_date) {
            const dueDate = new Date(invoice.due_date);
            const formattedDueDate = new Intl.DateTimeFormat('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            }).format(dueDate);
            doc.text(`Due Date: ${formattedDueDate}`, 14, 56);
          }

          doc.text(`Status: ${invoice.status || 'pending'}`, 14, 63);
          doc.text(`Total Amount: $${parseFloat(invoice.total_amount || 0).toFixed(2)}`, 14, 70);

          // Add line items table if available
          if (invoice.line_items && invoice.line_items.length > 0) {
            doc.text('Line Items:', 14, 80);

            // Prepare table data
            const tableColumn = ['Description', 'Quantity', 'Unit Price', 'Amount'];
            const tableRows = invoice.line_items.map((item) => [
              item.description || '',
              item.quantity || '',
              `$${parseFloat(item.unit_price || 0).toFixed(2)}`,
              `$${parseFloat(item.amount || 0).toFixed(2)}`,
            ]);

            // Add table to document
            doc.autoTable({
              startY: 85,
              head: [tableColumn],
              body: tableRows,
              theme: 'striped',
              headStyles: { fillColor: [66, 139, 202] },
            });
          }

          // Add notes if available
          if (invoice.notes) {
            const finalY = doc.lastAutoTable ? doc.lastAutoTable.finalY + 10 : 85;
            doc.text('Notes:', 14, finalY);
            doc.setFontSize(10);

            // Split notes into multiple lines if needed
            const splitNotes = doc.splitTextToSize(invoice.notes, 180);
            doc.text(splitNotes, 14, finalY + 7);
          }

          // Save the PDF
          doc.save(`invoice-${invoice.invoice_number || invoice.id}.pdf`);
        } catch (error) {
          console.error('Error generating PDF:', error);
        }
      });
    });
  },
};

// Dashboard services
export const dashboardService = {
  getData: () => api.get('/dashboard'),
  getInvoicesByMonth: () => api.get('/dashboard/invoices-by-month'),
  getTopVendors: () => api.get('/dashboard/top-vendors'),
  getTopItems: () => api.get('/dashboard/top-items'),

  // Get price alerts for dashboard
  getPriceAlerts: (params = {}) => {
    console.log('API Service: Fetching price alerts with params:', params);
    return api.get('/items/price-alerts', { params })
      .then((response) => {
        console.log('API Service: Price alerts fetched successfully', {
          alertCount: response.data?.alerts?.length || 0,
          totalCount: response.data?.total_count || 0,
        });
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error fetching price alerts:', error);
        throw error;
      });
  },

  // Get price changes
  getPriceChanges: (params = {}) => {
    console.log('API Service: Fetching price changes with params:', params);
    return api.get('/items/price-changes', { params })
      .then((response) => {
        console.log('API Service: Price changes fetched successfully', {
          changeCount: response.data?.price_changes?.length || 0,
        });
        return response;
      })
      .catch((error) => {
        console.error('API Service: Error fetching price changes:', error);
        throw error;
      });
  },
};

export default api;
