import { create } from 'zustand';
import { dashboardService } from '../services/api';

const useDashboardStore = create((set) => ({
  // Dashboard state
  dashboardData: null,
  invoicesByMonth: [],
  topVendors: [],
  topItems: [],
  priceAlerts: [],
  loading: false,
  error: null,

  // Fetch dashboard data
  fetchDashboardData: async () => {
    set({ loading: true, error: null });

    try {
      const response = await dashboardService.getData();
      set({
        dashboardData: response.data,
        loading: false,
      });
      return response.data;
    } catch (error) {
      set({
        loading: false,
        error: error.response?.data?.message || 'Error fetching dashboard data',
      });
      throw error;
    }
  },

  // Fetch invoices by month
  fetchInvoicesByMonth: async () => {
    set({ loading: true, error: null });

    try {
      const response = await dashboardService.getInvoicesByMonth();
      set({
        invoicesByMonth: response.data,
        loading: false,
      });
      return response.data;
    } catch (error) {
      // If the endpoint doesn't exist yet, generate mock data
      if (error.response?.status === 404) {
        const mockData = generateMockMonthlyData();
        set({
          invoicesByMonth: mockData,
          loading: false,
        });
        return mockData;
      }

      set({
        loading: false,
        error: error.response?.data?.message || 'Error fetching invoices by month',
      });
      throw error;
    }
  },

  // Fetch top vendors
  fetchTopVendors: async () => {
    set({ loading: true, error: null });

    try {
      const response = await dashboardService.getTopVendors();
      set({
        topVendors: response.data,
        loading: false,
      });
      return response.data;
    } catch (error) {
      // If the endpoint doesn't exist yet, generate mock data
      if (error.response?.status === 404) {
        const mockData = generateMockVendorData();
        set({
          topVendors: mockData,
          loading: false,
        });
        return mockData;
      }

      set({
        loading: false,
        error: error.response?.data?.message || 'Error fetching top vendors',
      });
      throw error;
    }
  },

  // Fetch top items
  fetchTopItems: async () => {
    set({ loading: true, error: null });

    try {
      const response = await dashboardService.getTopItems();
      set({
        topItems: response.data,
        loading: false,
      });
      return response.data;
    } catch (error) {
      // If the endpoint doesn't exist yet, generate mock data
      if (error.response?.status === 404) {
        const mockData = generateMockItemData();
        set({
          topItems: mockData,
          loading: false,
        });
        return mockData;
      }

      set({
        loading: false,
        error: error.response?.data?.message || 'Error fetching top items',
      });
      throw error;
    }
  },

  // Fetch all dashboard data
  fetchAllDashboardData: async () => {
    console.log('Dashboard Store: Starting fetchAllDashboardData');
    set({ loading: true, error: null });

    try {
      // Try to fetch real data first
      console.log('Dashboard Store: Making API calls...');
      const dashboardPromise = dashboardService.getData();
      const invoicesByMonthPromise = dashboardService.getInvoicesByMonth();
      const topVendorsPromise = dashboardService.getTopVendors();
      const topItemsPromise = dashboardService.getTopItems();
      const priceAlertsPromise = dashboardService.getPriceAlerts({ limit: 5 });

      // Handle each promise individually to provide better error messages
      const dashboardData = await dashboardPromise.catch((error) => {
        console.warn('Dashboard Store: Failed to fetch dashboard data:', error.message);
        console.log('Dashboard Store: Using mock dashboard data');
        return { data: generateMockDashboardData(), isMock: true };
      });

      const invoicesByMonth = await invoicesByMonthPromise.catch((error) => {
        console.warn('Dashboard Store: Failed to fetch invoices by month:', error.message);
        console.log('Dashboard Store: Using mock monthly data');
        return { data: generateMockMonthlyData(), isMock: true };
      });

      const topVendors = await topVendorsPromise.catch((error) => {
        console.warn('Dashboard Store: Failed to fetch top vendors:', error.message);
        console.log('Dashboard Store: Using mock vendor data');
        return { data: generateMockVendorData(), isMock: true };
      });

      const topItems = await topItemsPromise.catch((error) => {
        console.warn('Dashboard Store: Failed to fetch top items:', error.message);
        console.log('Dashboard Store: Using mock item data');
        return { data: generateMockItemData(), isMock: true };
      });

      const priceAlerts = await priceAlertsPromise.catch((error) => {
        console.warn('Dashboard Store: Failed to fetch price alerts:', error.message);
        console.log('Dashboard Store: Using mock alert data');
        return { data: generateMockAlertData(), isMock: true };
      });

      // Check if we're using any mock data
      const usingMockData =
        dashboardData.isMock || invoicesByMonth.isMock || topVendors.isMock || topItems.isMock || priceAlerts.isMock;

      console.log('Dashboard Store: Data fetched successfully', {
        dashboardData: dashboardData.data,
        invoicesByMonth: invoicesByMonth.data,
        topVendors: topVendors.data,
        topItems: topItems.data,
        priceAlerts: priceAlerts.data,
        usingMockData,
      });

      set({
        dashboardData: dashboardData.data,
        invoicesByMonth: invoicesByMonth.data,
        topVendors: topVendors.data,
        topItems: topItems.data,
        priceAlerts: priceAlerts.data,
        loading: false,
        // Set a warning if using mock data
        error: usingMockData
          ? 'Some data could not be loaded from the server. Showing sample data for demonstration.'
          : null,
      });

      return {
        dashboardData: dashboardData.data,
        invoicesByMonth: invoicesByMonth.data,
        topVendors: topVendors.data,
        topItems: topItems.data,
        priceAlerts: priceAlerts.data,
        usingMockData,
      };
    } catch (error) {
      console.error('Dashboard Store: Error in fetchAllDashboardData:', error);

      // Fallback to all mock data if everything fails
      console.log('Dashboard Store: Complete fallback to mock data');
      const mockDashboardData = generateMockDashboardData();
      const mockMonthlyData = generateMockMonthlyData();
      const mockVendorData = generateMockVendorData();
      const mockItemData = generateMockItemData();
      const mockAlertData = generateMockAlertData();

      set({
        dashboardData: mockDashboardData,
        invoicesByMonth: mockMonthlyData,
        topVendors: mockVendorData,
        topItems: mockItemData,
        priceAlerts: mockAlertData,
        loading: false,
        error: 'Unable to connect to server. Showing sample data for demonstration.',
      });

      return {
        dashboardData: mockDashboardData,
        invoicesByMonth: mockMonthlyData,
        topVendors: mockVendorData,
        topItems: mockItemData,
        priceAlerts: mockAlertData,
        usingMockData: true,
      };
    }
  },
}));

// Helper functions to generate mock data for development
function generateMockDashboardData() {
  return {
    totalInvoices: 42,
    totalAmount: 12750.85,
    averageAmount: 303.59,
    pendingInvoices: 8,
    approvedInvoices: 34,
  };
}

function generateMockMonthlyData() {
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  const currentMonth = new Date().getMonth();

  const mockData = months
    .map((month, index) => ({
      name: month,
      amount: index <= currentMonth ? Math.floor(Math.random() * 5000) + 500 : 0,
    }))
    .filter((_, index) => index <= currentMonth);

  console.log('Dashboard Store: Generated mock monthly data:', mockData);
  return mockData;
}

function generateMockVendorData() {
  const vendors = [
    { name: 'Office Supplies Inc.', amount: 3245.67 },
    { name: 'Tech Solutions', amount: 2890.5 },
    { name: 'Catering Services', amount: 1875.25 },
    { name: 'Cleaning Crew', amount: 1250.0 },
    { name: 'Marketing Agency', amount: 3500.0 },
  ];

  console.log('Dashboard Store: Generated mock vendor data:', vendors);
  return vendors;
}

function generateMockItemData() {
  const items = [
    { name: 'Office Supplies', amount: 2345.67 },
    { name: 'Software Licenses', amount: 3890.5 },
    { name: 'Catering', amount: 1275.25 },
    { name: 'Cleaning Services', amount: 950.0 },
    { name: 'Marketing', amount: 2500.0 },
  ];

  console.log('Dashboard Store: Generated mock item data:', items);
  return items;
}

function generateMockAlertData() {
  const alerts = [
    {
      id: 'price_alert_1',
      type: 'price',
      severity: 'high',
      alert_type: 'critical',
      title: 'Price Increased',
      message: 'Chicken breasts price increased by 18.5% from Sysco Foods',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      item_id: 'item_1',
      item_name: 'Chicken Breasts',
      vendor: 'Sysco Foods',
      old_price: 4.25,
      current_price: 5.04,
      percentage_change: 18.5,
      invoice_id: 'inv_123',
      invoice_number: 'INV-2024-001'
    },
    {
      id: 'price_alert_2',
      type: 'price',
      severity: 'medium',
      alert_type: 'warning',
      title: 'Price Increased',
      message: 'Ground beef price increased by 12.3% from US Foods',
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      item_id: 'item_2',
      item_name: 'Ground Beef',
      vendor: 'US Foods',
      old_price: 6.50,
      current_price: 7.30,
      percentage_change: 12.3,
      invoice_id: 'inv_124',
      invoice_number: 'INV-2024-002'
    },
    {
      id: 'price_alert_3',
      type: 'price',
      severity: 'low',
      alert_type: 'info',
      title: 'Price Decreased',
      message: 'Tomatoes price decreased by 8.2% from Fresh Direct',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      item_id: 'item_3',
      item_name: 'Tomatoes',
      vendor: 'Fresh Direct',
      old_price: 3.20,
      current_price: 2.94,
      percentage_change: -8.2,
      invoice_id: 'inv_125',
      invoice_number: 'INV-2024-003'
    }
  ];

  console.log('Dashboard Store: Generated mock alert data:', alerts);
  return alerts;
}

export default useDashboardStore;
