from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from services.item import ItemService
from utils.error_handlers import api_error_handler, validate_request_data, NotFoundError
from utils.limiter import limiter, DEFAULT_LIMIT, get_user_id
from models.models import User

items_bp = Blueprint('items', __name__)

@items_bp.route('', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_items():
    """Get all items for the current user's restaurant."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    items = ItemService.get_all_items(user.restaurant_id)
    
    return jsonify({
        'items': [item.to_dict() for item in items]
    }), 200

@items_bp.route('/<item_id>', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_item(item_id):
    """Get a specific item by ID."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    item, message, status_code = ItemService.get_item_by_id(item_id, user.restaurant_id)
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'item': item.to_dict()
    }), status_code

@items_bp.route('', methods=['POST'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def create_item():
    """Create a new item."""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # Validate required fields
    validate_request_data(data, ['name'])
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    item, message, status_code = ItemService.create_item(data, user.restaurant_id)
    
    if status_code != 201:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'item': item.to_dict()
    }), status_code

@items_bp.route('/<item_id>', methods=['PUT'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def update_item(item_id):
    """Update an existing item."""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    item, message, status_code = ItemService.update_item(item_id, data, user.restaurant_id)
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'item': item.to_dict()
    }), status_code

@items_bp.route('/<item_id>', methods=['DELETE'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def delete_item(item_id):
    """Delete an item."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    success, message, status_code = ItemService.delete_item(item_id, user.restaurant_id)
    
    return jsonify({'message': message}), status_code

@items_bp.route('/<item_id>/price-history', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_price_history(item_id):
    """Get price history for an item."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    # Get date range parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Convert string dates to datetime objects
    if start_date:
        try:
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'message': 'Invalid start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)'}), 400
    
    if end_date:
        try:
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        except ValueError:
            return jsonify({'message': 'Invalid end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)'}), 400
    
    price_history, message, status_code = ItemService.get_price_history(
        item_id, user.restaurant_id, start_date, end_date
    )
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'price_history': [ph.to_dict() for ph in price_history]
    }), status_code

@items_bp.route('/<item_id>/price-history', methods=['POST'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def add_price_history(item_id):
    """Add a new price history record."""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # Validate required fields
    validate_request_data(data, ['price'])
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    price_history, message, status_code = ItemService.add_price_history(
        item_id, data, user.restaurant_id
    )
    
    if status_code != 201:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'price_history': price_history.to_dict()
    }), status_code

@items_bp.route('/price-changes', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_price_changes():
    """Get significant price changes for items."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    # Get filter parameters
    threshold = request.args.get('threshold')
    days = request.args.get('days', 30)
    item_id = request.args.get('item_id')
    category = request.args.get('category')
    
    # Convert string parameters to appropriate types
    if threshold:
        try:
            threshold = float(threshold)
        except ValueError:
            return jsonify({'message': 'Invalid threshold. Must be a number.'}), 400
    
    if days:
        try:
            days = int(days)
        except ValueError:
            return jsonify({'message': 'Invalid days. Must be an integer.'}), 400
    
    price_changes, message, status_code = ItemService.get_price_changes(
        user.restaurant_id, threshold, days, item_id, category
    )
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'price_changes': price_changes
    }), status_code


@items_bp.route('/price-alerts', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def get_price_alerts():
    """Get current price alerts for the dashboard."""
    user_id = get_jwt_identity()

    # Get query parameters
    limit = request.args.get('limit', 10)
    severity = request.args.get('severity')  # 'high', 'medium', 'low'

    # Convert limit to int
    try:
        limit = int(limit)
    except ValueError:
        limit = 10

    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")

    # Get recent price changes (last 7 days for alerts)
    price_changes, message, status_code = ItemService.get_price_changes(
        user.restaurant_id, threshold=None, days=7
    )

    if status_code != 200:
        return jsonify({'message': message}), status_code

    # Convert price changes to alert format
    alerts = []
    for change in price_changes:
        # Determine severity based on percentage change
        abs_change = abs(change['percentage_change'])
        if abs_change >= 25:
            alert_severity = 'high'
            alert_type = 'critical'
        elif abs_change >= 15:
            alert_severity = 'medium'
            alert_type = 'warning'
        else:
            alert_severity = 'low'
            alert_type = 'info'

        # Skip if severity filter doesn't match
        if severity and alert_severity != severity:
            continue

        # Create alert message
        direction = 'increased' if change['percentage_change'] > 0 else 'decreased'
        alert_message = f"{change['item']['name']} price {direction} by {abs_change:.1f}% from {change['vendor']}"

        alerts.append({
            'id': f"price_alert_{change['item']['id']}_{change['current_price_date']}",
            'type': 'price',
            'severity': alert_severity,
            'alert_type': alert_type,
            'title': f"Price {direction.title()}",
            'message': alert_message,
            'date': change['current_price_date'],
            'item_id': change['item']['id'],
            'item_name': change['item']['name'],
            'vendor': change['vendor'],
            'old_price': change['old_price'],
            'current_price': change['current_price'],
            'percentage_change': change['percentage_change'],
            'invoice_id': change['invoice_id'],
            'invoice_number': change['invoice_number']
        })

        # Limit results
        if len(alerts) >= limit:
            break

    return jsonify({
        'message': 'Price alerts retrieved successfully',
        'alerts': alerts,
        'total_count': len(alerts),
        'filters': {
            'limit': limit,
            'severity': severity
        }
    }), 200


@items_bp.route('/match-line-item/<line_item_id>', methods=['GET'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def match_line_item(line_item_id):
    """Get potential item matches for a line item."""
    user_id = get_jwt_identity()
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    candidates, message, status_code = ItemService.match_line_item_to_item(
        line_item_id, user.restaurant_id
    )
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'candidates': candidates
    }), status_code

@items_bp.route('/link-line-item/<line_item_id>', methods=['POST'])
@jwt_required()
@limiter.limit(DEFAULT_LIMIT, key_func=get_user_id)
@api_error_handler
def link_line_item(line_item_id):
    """Link a line item to a standard item."""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # Validate required fields
    validate_request_data(data, ['item_id'])
    
    # Get user's restaurant_id
    user = User.query.get(user_id)
    if not user or not user.restaurant_id:
        raise NotFoundError("User or restaurant not found")
    
    # Get create_price_history parameter (default to True)
    create_price_history = data.get('create_price_history', True)
    
    line_item, message, status_code = ItemService.link_line_item_to_item(
        line_item_id, data['item_id'], user.restaurant_id, create_price_history
    )
    
    if status_code != 200:
        return jsonify({'message': message}), status_code
    
    return jsonify({
        'message': message,
        'line_item': line_item.to_dict()
    }), status_code