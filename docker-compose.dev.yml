version: '3.8'

services:
  # PostgreSQL database
  db:
    image: postgres:15-alpine
    container_name: inventory-tracker-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER:-inventoryapp}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-devpassword}
      POSTGRES_DB: ${DB_NAME:-inventorydb}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${DB_PORT:-5436}:5432"
    networks:
      - inventory-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U inventoryapp"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for rate limiting and caching
  redis:
    image: redis:alpine
    container_name: inventory-tracker-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - inventory-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: inventory-tracker-backend
    restart: unless-stopped
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    ports:
      - "${BACKEND_PORT:-5001}:5001"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      FLASK_APP: app.py
      FLASK_ENV: development
      FLASK_DEBUG: 1
      SQLALCHEMY_DATABASE_URI: postgresql://${DB_USER:-inventoryapp}:${DB_PASSWORD:-devpassword}@db:5432/${DB_NAME:-inventorydb}
      REDIS_URL: redis://redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-dev-secret-key}
      CORS_ALLOWED_ORIGINS: http://localhost:3000,http://localhost:5001,http://*************:3000,http://*************:3000,http://*************:5001,http://*************:5001
      UPLOAD_FOLDER: /app/uploads
      LOG_LEVEL: DEBUG
      LOG_FORMAT: both
      # OCR Configuration
      MINDEE_API_KEY: ${MINDEE_API_KEY}
      OCR_PROVIDER: mindee
    networks:
      - inventory-network
    command: >
      sh -c "
        pip install pyotp qrcode &&
        sed -i 's/from mindee import Client, product/from mindee import Client/' /app/services/mindee_client_service.py &&
        sed -i 's/from mindee.parsing.common import Prediction/# from mindee.parsing.common import Prediction/' /app/services/mindee_client_service.py &&
        sed -i 's/result = self.client.parse(product.InvoiceV4, input_doc)/result = self.client.parse(\"invoices\", input_doc)/' /app/services/mindee_client_service.py &&
        sed -i 's/result = self.client.enqueue_and_parse(product.GeneratedV1, input_doc, endpoint=endpoint)/result = self.client.enqueue_and_parse(\"custom\", input_doc, endpoint=endpoint)/' /app/services/mindee_client_service.py &&
        sed -i 's/# logger.info(f\"Logging initialized - Level: {LOG_LEVEL}, Format: {LOG_FORMAT}\")/print(\"Logging initialized\")/' /app/utils/logger.py &&
        # Create database tables directly
        python -c 'from app import create_app; from models.models import db; app = create_app(); app.app_context().push(); db.create_all()' &&
        python -m flask run --host=0.0.0.0 --port=5001
      "

  # Frontend development server
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: inventory-tracker-frontend
    restart: unless-stopped
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    depends_on:
      - backend
    environment:
      NODE_ENV: development
      REACT_APP_API_URL: http://*************:5001/api/v1
      CHOKIDAR_USEPOLLING: "true"
      HOST: 0.0.0.0
    networks:
      - inventory-network
    command: npm start

  # Adminer for database management (optional)
  adminer:
    image: adminer
    container_name: inventory-tracker-adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    depends_on:
      - db
    networks:
      - inventory-network

networks:
  inventory-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: